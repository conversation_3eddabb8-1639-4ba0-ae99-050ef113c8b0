﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void Mono.Security.ASN1::.ctor(System.Byte)
extern void ASN1__ctor_mA9AE2197367C1E13DBFDA67E0A383167F52CC114 (void);
// 0x00000002 System.Void Mono.Security.ASN1::.ctor(System.Byte,System.Byte[])
extern void ASN1__ctor_mAA538F9E1BE0DE739E9747BC3BC71DC030B018AA (void);
// 0x00000003 System.Void Mono.Security.ASN1::.ctor(System.Byte[])
extern void ASN1__ctor_m950BFCCF44A987ACBA12142624AA222200EE503E (void);
// 0x00000004 System.Int32 Mono.Security.ASN1::get_Count()
extern void ASN1_get_Count_mBE45E73126FAD2694E9059CAC53B7AC9A5F60833 (void);
// 0x00000005 System.Byte Mono.Security.ASN1::get_Tag()
extern void ASN1_get_Tag_m1984CF0DDF54424E61BA3650D93CBA0DCB58F232 (void);
// 0x00000006 System.Byte[] Mono.Security.ASN1::get_Value()
extern void ASN1_get_Value_mA6F9BE5AC19AC060AC42673C8FD5AA864EA046B6 (void);
// 0x00000007 System.Void Mono.Security.ASN1::set_Value(System.Byte[])
extern void ASN1_set_Value_mAFFA885810928715B379EAD478AA3961E8ACD589 (void);
// 0x00000008 Mono.Security.ASN1 Mono.Security.ASN1::Add(Mono.Security.ASN1)
extern void ASN1_Add_m4C61487A6CCF48D5CEB0D97B248FE31F9FCD849F (void);
// 0x00000009 System.Byte[] Mono.Security.ASN1::GetBytes()
extern void ASN1_GetBytes_m3B7DABFDBE6BF7F9C926E4C8A16FC6BE6D1CE67B (void);
// 0x0000000A System.Void Mono.Security.ASN1::Decode(System.Byte[],System.Int32&,System.Int32)
extern void ASN1_Decode_mC4CF3CB2CC1DB454AA9C720BA79520956FB1F77B (void);
// 0x0000000B System.Void Mono.Security.ASN1::DecodeTLV(System.Byte[],System.Int32&,System.Byte&,System.Int32&,System.Byte[]&)
extern void ASN1_DecodeTLV_mD4465394202DA7B0D37B9453CDE039233969E9DF (void);
// 0x0000000C Mono.Security.ASN1 Mono.Security.ASN1::get_Item(System.Int32)
extern void ASN1_get_Item_mF105DA24F3BE9FA3697229CF99B1602B736B647F (void);
// 0x0000000D System.String Mono.Security.ASN1::ToString()
extern void ASN1_ToString_m4995F083B02F8FEF578ECA6EE73A257821F50A00 (void);
// 0x0000000E Mono.Security.ASN1 Mono.Security.ASN1Convert::FromInt32(System.Int32)
extern void ASN1Convert_FromInt32_mACAC096211E525F124BE0D50D90524ADCB6EA198 (void);
// 0x0000000F System.Int32 Mono.Security.ASN1Convert::ToInt32(Mono.Security.ASN1)
extern void ASN1Convert_ToInt32_m956785EB4A235575C21677C16D2F6CBE54787032 (void);
// 0x00000010 System.String Mono.Security.ASN1Convert::ToOid(Mono.Security.ASN1)
extern void ASN1Convert_ToOid_mBCE4FD3970C556190FB00A6AD409A6ABB4C627D8 (void);
// 0x00000011 System.Byte[] Mono.Security.BitConverterLE::GetUIntBytes(System.Byte*)
extern void BitConverterLE_GetUIntBytes_mED0A55F565721091E851FD6108E128C3CBCB87F0 (void);
// 0x00000012 System.Byte[] Mono.Security.BitConverterLE::GetBytes(System.Int32)
extern void BitConverterLE_GetBytes_mEEFE00015D501FBBD32225D9C45A2C2A0673E9C7 (void);
// 0x00000013 System.String Mono.Security.Cryptography.CryptoConvert::ToHex(System.Byte[])
extern void CryptoConvert_ToHex_m1A0AD4D32CEEC47D3C60CB2E4D05A935C62F261A (void);
static Il2CppMethodPointer s_methodPointers[19] = 
{
	ASN1__ctor_mA9AE2197367C1E13DBFDA67E0A383167F52CC114,
	ASN1__ctor_mAA538F9E1BE0DE739E9747BC3BC71DC030B018AA,
	ASN1__ctor_m950BFCCF44A987ACBA12142624AA222200EE503E,
	ASN1_get_Count_mBE45E73126FAD2694E9059CAC53B7AC9A5F60833,
	ASN1_get_Tag_m1984CF0DDF54424E61BA3650D93CBA0DCB58F232,
	ASN1_get_Value_mA6F9BE5AC19AC060AC42673C8FD5AA864EA046B6,
	ASN1_set_Value_mAFFA885810928715B379EAD478AA3961E8ACD589,
	ASN1_Add_m4C61487A6CCF48D5CEB0D97B248FE31F9FCD849F,
	ASN1_GetBytes_m3B7DABFDBE6BF7F9C926E4C8A16FC6BE6D1CE67B,
	ASN1_Decode_mC4CF3CB2CC1DB454AA9C720BA79520956FB1F77B,
	ASN1_DecodeTLV_mD4465394202DA7B0D37B9453CDE039233969E9DF,
	ASN1_get_Item_mF105DA24F3BE9FA3697229CF99B1602B736B647F,
	ASN1_ToString_m4995F083B02F8FEF578ECA6EE73A257821F50A00,
	ASN1Convert_FromInt32_mACAC096211E525F124BE0D50D90524ADCB6EA198,
	ASN1Convert_ToInt32_m956785EB4A235575C21677C16D2F6CBE54787032,
	ASN1Convert_ToOid_mBCE4FD3970C556190FB00A6AD409A6ABB4C627D8,
	BitConverterLE_GetUIntBytes_mED0A55F565721091E851FD6108E128C3CBCB87F0,
	BitConverterLE_GetBytes_mEEFE00015D501FBBD32225D9C45A2C2A0673E9C7,
	CryptoConvert_ToHex_m1A0AD4D32CEEC47D3C60CB2E4D05A935C62F261A,
};
static const int32_t s_InvokerIndices[19] = 
{
	4808,
	2161,
	4906,
	6091,
	6026,
	6120,
	4906,
	4335,
	6120,
	1316,
	423,
	4329,
	6120,
	9836,
	9728,
	9840,
	9825,
	9836,
	9840,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Mono_Security_CodeGenModule;
const Il2CppCodeGenModule g_Mono_Security_CodeGenModule = 
{
	"Mono.Security.dll",
	19,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
