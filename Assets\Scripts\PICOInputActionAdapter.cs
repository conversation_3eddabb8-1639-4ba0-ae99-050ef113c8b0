using UnityEngine;
using UnityEngine.InputSystem;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
#endif

/// <summary>
/// PICO Input Action适配器
/// 使用Unity Input System来处理PICO控制器输入
/// </summary>
public class PICOInputActionAdapter : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;
    
    [Header("输入设置")]
    [SerializeField] private bool enableInputActions = true;
    [SerializeField] private bool showInputDebug = true;
    
    [Header("输入动作引用")]
    [SerializeField] private InputActionReference triggerAction;
    [SerializeField] private InputActionReference primaryButtonAction;
    [SerializeField] private InputActionReference secondaryButtonAction;
    [SerializeField] private InputActionReference menuButtonAction;
    
    [Header("备用键盘输入")]
    [SerializeField] private bool enableKeyboardFallback = true;
    [SerializeField] private KeyCode fallbackTriggerKey = KeyCode.T;
    [SerializeField] private KeyCode fallbackPrimaryKey = KeyCode.P;
    [SerializeField] private KeyCode fallbackSecondaryKey = KeyCode.G;
    [SerializeField] private KeyCode fallbackMenuKey = KeyCode.F1;

    // 输入冷却
    private float lastInputTime = 0f;
    private float inputCooldown = 0.5f;

    void Start()
    {
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }
        
        SetupInputActions();
        Debug.Log("[PICOInputActionAdapter] PICO Input Action适配器已启动");
        ShowInputMapping();
    }

    void Update()
    {
        // 备用键盘输入
        if (enableKeyboardFallback)
        {
            HandleKeyboardInput();
        }
    }

    /// <summary>
    /// 设置输入动作
    /// </summary>
    private void SetupInputActions()
    {
        if (!enableInputActions) return;

        // 如果没有手动设置InputActionReference，尝试自动创建
        if (triggerAction == null || primaryButtonAction == null || 
            secondaryButtonAction == null || menuButtonAction == null)
        {
            CreateDefaultInputActions();
        }

        // 启用输入动作并绑定事件
        EnableInputAction(triggerAction, OnTriggerPressed, "扳机键");
        EnableInputAction(primaryButtonAction, OnPrimaryButtonPressed, "主按钮");
        EnableInputAction(secondaryButtonAction, OnSecondaryButtonPressed, "副按钮");
        EnableInputAction(menuButtonAction, OnMenuButtonPressed, "菜单按钮");
    }

    /// <summary>
    /// 创建默认输入动作
    /// </summary>
    private void CreateDefaultInputActions()
    {
        Debug.Log("[PICOInputActionAdapter] 创建默认输入动作...");
        
        // 注意：这里创建的是基本的InputAction，实际项目中建议使用InputActionAsset
        if (triggerAction == null)
        {
            var triggerInputAction = new InputAction("Trigger", InputActionType.Button);
            triggerInputAction.AddBinding("<XRController>{RightHand}/triggerButton");
            triggerInputAction.AddBinding("<XRController>{LeftHand}/triggerButton");
        }
        
        // 类似地为其他按钮创建InputAction...
        Debug.LogWarning("[PICOInputActionAdapter] 建议在Inspector中手动设置InputActionReference");
    }

    /// <summary>
    /// 启用输入动作并绑定事件
    /// </summary>
    private void EnableInputAction(InputActionReference actionRef, System.Action<InputAction.CallbackContext> callback, string actionName)
    {
        if (actionRef != null && actionRef.action != null)
        {
            actionRef.action.performed += callback;
            actionRef.action.Enable();
            Debug.Log($"✓ 已启用{actionName}输入动作");
        }
        else
        {
            Debug.LogWarning($"⚠ {actionName}输入动作引用为空");
        }
    }

    /// <summary>
    /// 扳机键按下事件
    /// </summary>
    private void OnTriggerPressed(InputAction.CallbackContext context)
    {
        if (!CanProcessInput()) return;
        
        Debug.Log("[PICOInputActionAdapter] 扳机键按下 - 执行第一部分功能：装配区域移动到基于摄像机的固定位置");
        ExecuteFirstPartFunction();
    }

    /// <summary>
    /// 主按钮按下事件
    /// </summary>
    private void OnPrimaryButtonPressed(InputAction.CallbackContext context)
    {
        if (!CanProcessInput()) return;
        
        Debug.Log("[PICOInputActionAdapter] 主按钮按下 - 执行第二部分功能：让装配面朝向摄像机");
        ExecuteSecondPartFunction();
    }

    /// <summary>
    /// 副按钮按下事件
    /// </summary>
    private void OnSecondaryButtonPressed(InputAction.CallbackContext context)
    {
        if (!CanProcessInput()) return;
        
        Debug.Log("[PICOInputActionAdapter] 副按钮按下 - 重置装配区域位置");
        ExecuteResetFunction();
    }

    /// <summary>
    /// 菜单按钮按下事件
    /// </summary>
    private void OnMenuButtonPressed(InputAction.CallbackContext context)
    {
        if (!CanProcessInput()) return;
        
        Debug.Log("[PICOInputActionAdapter] 菜单按钮按下 - 切换调试界面");
        ExecuteToggleDebugFunction();
    }

    /// <summary>
    /// 处理键盘输入（备用）
    /// </summary>
    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(fallbackTriggerKey))
        {
            OnTriggerPressed(new InputAction.CallbackContext());
        }
        
        if (Input.GetKeyDown(fallbackPrimaryKey))
        {
            OnPrimaryButtonPressed(new InputAction.CallbackContext());
        }
        
        if (Input.GetKeyDown(fallbackSecondaryKey))
        {
            OnSecondaryButtonPressed(new InputAction.CallbackContext());
        }
        
        if (Input.GetKeyDown(fallbackMenuKey))
        {
            OnMenuButtonPressed(new InputAction.CallbackContext());
        }
    }

    /// <summary>
    /// 检查是否可以处理输入（防止重复触发）
    /// </summary>
    private bool CanProcessInput()
    {
        if (Time.time - lastInputTime < inputCooldown)
        {
            return false;
        }
        
        lastInputTime = Time.time;
        return true;
    }

    /// <summary>
    /// 执行第一部分功能
    /// </summary>
    private void ExecuteFirstPartFunction()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICOInputActionAdapter] VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.TestCameraBasedPositioning();
    }

    /// <summary>
    /// 执行第二部分功能
    /// </summary>
    private void ExecuteSecondPartFunction()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICOInputActionAdapter] VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.TestOrientation();
    }

    /// <summary>
    /// 执行重置功能
    /// </summary>
    private void ExecuteResetFunction()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICOInputActionAdapter] VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.ResetPosition();
    }

    /// <summary>
    /// 执行切换调试界面功能
    /// </summary>
    private void ExecuteToggleDebugFunction()
    {
        // 这里可以添加切换调试界面的逻辑
        Debug.Log("[PICOInputActionAdapter] 切换调试界面功能");
    }

    /// <summary>
    /// 显示输入映射
    /// </summary>
    [ContextMenu("显示输入映射")]
    public void ShowInputMapping()
    {
        string mapping = @"
PICOInputActionAdapter 输入映射：

VR控制器输入（Input Action）：
- 扳机键 (Trigger) → 第一部分功能：装配区域移动到基于摄像机的固定位置
- 主按钮 (A/X) → 第二部分功能：让装配面朝向摄像机
- 副按钮 (B/Y) → 重置装配区域位置
- 菜单按钮 (Menu) → 切换调试界面

键盘备用输入：
- T键 → 第一部分功能
- P键 → 第二部分功能
- G键 → 重置功能
- F1键 → 切换调试界面

配置说明：
1. 在Inspector中设置InputActionReference
2. 或者使用PICO SDK提供的预设InputActionAsset
3. 确保InputActionManager已正确配置
4. 每次输入有0.5秒冷却时间防止重复触发
";
        Debug.Log(mapping);
    }

    /// <summary>
    /// 手动测试所有功能
    /// </summary>
    [ContextMenu("测试所有功能")]
    public void TestAllFunctions()
    {
        StartCoroutine(TestSequence());
    }

    /// <summary>
    /// 测试序列
    /// </summary>
    private System.Collections.IEnumerator TestSequence()
    {
        Debug.Log("[PICOInputActionAdapter] 开始自动测试序列...");
        
        Debug.Log("1. 测试第一部分功能...");
        ExecuteFirstPartFunction();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("2. 测试第二部分功能...");
        ExecuteSecondPartFunction();
        yield return new WaitForSeconds(3f);
        
        Debug.Log("3. 重置装配区域位置...");
        ExecuteResetFunction();
        yield return new WaitForSeconds(2f);
        
        Debug.Log("[PICOInputActionAdapter] 自动测试序列完成！");
    }

    void OnDestroy()
    {
        // 禁用输入动作
        DisableInputAction(triggerAction);
        DisableInputAction(primaryButtonAction);
        DisableInputAction(secondaryButtonAction);
        DisableInputAction(menuButtonAction);
    }

    /// <summary>
    /// 禁用输入动作
    /// </summary>
    private void DisableInputAction(InputActionReference actionRef)
    {
        if (actionRef != null && actionRef.action != null)
        {
            actionRef.action.Disable();
        }
    }
}
