%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &202364687
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 202364688}
  - component: {fileID: 4778211696441940833}
  - component: {fileID: 202364692}
  - component: {fileID: 942810691211101373}
  m_Layer: 0
  m_Name: Left Controller
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &202364688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 202364687}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1666320186578454293}
  - {fileID: 9013359448673381486}
  - {fileID: 3881409846907809176}
  - {fileID: 1319746309}
  m_Father: {fileID: 1680501587}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4778211696441940833
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 202364687}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9ac216f0eb04754b1d938aac6380b31, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ManipulationInteractionGroup: {fileID: 942810691211101373}
  m_DirectInteractor: {fileID: 2434299456458490401}
  m_RayInteractor: {fileID: 285389467476424711}
  m_TeleportInteractor: {fileID: 1319746312}
  m_TeleportModeActivate: {fileID: 1263111715868034790, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TeleportModeCancel: {fileID: 737890489006591557, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_Turn: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SnapTurn: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_Move: {fileID: 6972639530819350904, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIScroll: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SmoothMotionEnabled: 1
  m_SmoothTurnEnabled: 0
  m_UIScrollingEnabled: 1
  m_RayInteractorChanged:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3752199730057449385}
        m_TargetAssemblyTypeName: UnityEngine.XR.Interaction.Toolkit.Inputs.XRTransformStabilizer,
          Unity.XR.Interaction.Toolkit
        m_MethodName: set_aimTarget
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &202364692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 202364687}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UpdateTrackingType: 0
  m_EnableInputTracking: 1
  m_EnableInputActions: 1
  m_ModelPrefab: {fileID: 8270855663187062767, guid: 1392f805216c47742996d4742c80721c, type: 3}
  m_ModelParent: {fileID: 202364688}
  m_Model: {fileID: 0}
  m_AnimateModel: 0
  m_ModelSelectTransition: 
  m_ModelDeSelectTransition: 
  m_PositionAction:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: 8b170a9b-132e-486d-947e-6a244d4362ea
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -2024308242397127297, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 080819c2-8547-4beb-8522-e6356be16fb1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 8248158260566104461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_IsTrackedAction:
    m_UseReference: 1
    m_Action:
      m_Name: Is Tracked
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 22c1da5c-d38f-4253-a25c-fe94205f2ec5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 1
    m_Reference: {fileID: 840156964685210860, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TrackingStateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: f3874727-df53-4207-8cd4-6248164663d7
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 684395432459739428, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SelectAction:
    m_UseReference: 1
    m_Action:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 8e000d1c-13a4-4cc0-ad37-f2e125874399
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -6131295136447488360, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SelectActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Select Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: e015d020-ed5c-40b6-b968-fa9881521f0e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 6558622148059887818, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 3995f9f4-6aa7-409a-80d2-5f7ea1464fde
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5982496924579745919, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Activate Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 492aea1c-7d58-4cb0-8e3c-257d2f651c04
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIPressAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: db89d01c-df6f-4954-b868-103dd5bdb514
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -6395602842196007441, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIPressActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 6258f0cd-e000-49ea-b3b6-7c930f12c390
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 71106601250685021, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIScrollAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: b74fcfe3-d94d-4bf1-960a-364568ffe66b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_HapticDeviceAction:
    m_UseReference: 1
    m_Action:
      m_Name: Haptic Device
      m_Type: 2
      m_ExpectedControlType: 
      m_Id: 3e09b626-c80d-40ec-9592-eb3fe89c2038
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8785819595477538065, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: Rotate Anchor
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 3dca8766-e652-4e78-8406-420aa73ba338
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -7363382999065477798, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_DirectionalAnchorRotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Directional Anchor Rotation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 7d323aae-15a7-4c32-a2b9-0653cb108725
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8811388872089202044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TranslateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: Translate Anchor
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: e873605e-6a95-4389-8fbe-39069340ba92
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 7779212132400271959, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ScaleToggleAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: a1912586-fdc4-4079-b714-faafc085fd22
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -335775248641796371, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ScaleDeltaAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Delta
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 61322b52-a380-4dd9-9782-1091163d1509
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -1636515391019944688, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ButtonPressPoint: 0.5
--- !u!114 &942810691211101373
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 202364687}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4a50d88b55b45648927679791f472de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GroupName: Left
  m_InteractionManager: {fileID: 0}
  m_StartingGroupMembers:
  - {fileID: 4343660526480754339}
  - {fileID: 2434299456458490401}
  - {fileID: 285389467476424711}
  m_StartingInteractionOverridesMap:
  - groupMember: {fileID: 4343660526480754339}
    overrideGroupMembers:
    - {fileID: 2434299456458490401}
--- !u!1 &**********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1670256625}
  - component: {fileID: 5663893676086941514}
  - component: {fileID: 1670256628}
  - component: {fileID: 6678509202150728127}
  m_Layer: 0
  m_Name: Right Controller
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1670256625
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3954319948395782924}
  - {fileID: 1096734238491090331}
  - {fileID: 3921468432820449555}
  - {fileID: 2449787133337329436}
  m_Father: {fileID: 1680501587}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5663893676086941514
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9ac216f0eb04754b1d938aac6380b31, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ManipulationInteractionGroup: {fileID: 6678509202150728127}
  m_DirectInteractor: {fileID: 6045481230268494804}
  m_RayInteractor: {fileID: 19064736505062540}
  m_TeleportInteractor: {fileID: 2449787133337329425}
  m_TeleportModeActivate: {fileID: -8061240218431744966, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TeleportModeCancel: {fileID: 2307464322626738743, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_Turn: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SnapTurn: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_Move: {fileID: -8198699208435500284, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIScroll: {fileID: -6756787485274679044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SmoothMotionEnabled: 0
  m_SmoothTurnEnabled: 0
  m_UIScrollingEnabled: 1
  m_RayInteractorChanged:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1801942220539511183}
        m_TargetAssemblyTypeName: UnityEngine.XR.Interaction.Toolkit.Inputs.XRTransformStabilizer,
          Unity.XR.Interaction.Toolkit
        m_MethodName: set_aimTarget
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &1670256628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UpdateTrackingType: 0
  m_EnableInputTracking: 1
  m_EnableInputActions: 1
  m_ModelPrefab: {fileID: 3475118261464492563, guid: 9f3369e30fbd31f4bb596b1a99babe83, type: 3}
  m_ModelParent: {fileID: 1670256625}
  m_Model: {fileID: 0}
  m_AnimateModel: 0
  m_ModelSelectTransition: 
  m_ModelDeSelectTransition: 
  m_PositionAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -3326005586356538449, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 5101698808175986029, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_IsTrackedAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 1
    m_Reference: {fileID: -7044516463258014562, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TrackingStateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 008dba4e-870a-43fb-9a1f-1a7bc3ecec0c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -1277054153949319361, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SelectAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 187161793506945269, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SelectActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Select Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 6b1e5826-d74e-452e-ab31-5d6eae6f407e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -1758520528963094988, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 83097790271614945, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Activate Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 98d3d870-d1c9-4fbe-9790-8d0c2cb9ffc0
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIPressAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 3279264004350380116, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIPressActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: bf4ab5bd-3648-4de6-a1f6-8e879b2612c2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5908353012961274365, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIScrollAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: a6c0ac1e-4065-4abc-ac84-e81172fbfdd4
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -6756787485274679044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_HapticDeviceAction:
    m_UseReference: 1
    m_Action:
      m_Name: Haptic Device
      m_Type: 2
      m_ExpectedControlType: 
      m_Id: 59ea1b94-e9f8-4049-ab97-5920b11143a5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8222252007134549311, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5913262927076077117, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_DirectionalAnchorRotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Directional Anchor Rotation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 72b93609-c58e-411b-a958-c221860f8269
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -440298646266941818, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TranslateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: 
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 875253871413052681, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ScaleToggleAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 08507162-a3bd-4e4f-9854-0b3051398d03
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -2524354804938687746, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ScaleDeltaAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Delta
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 65d9ae9e-5581-4b2c-a1f5-2ba0614443e2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -6447266317303757838, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ButtonPressPoint: 0.5
--- !u!114 &6678509202150728127
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4a50d88b55b45648927679791f472de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GroupName: Right
  m_InteractionManager: {fileID: 0}
  m_StartingGroupMembers:
  - {fileID: 2141651114331267770}
  - {fileID: 6045481230268494804}
  - {fileID: 19064736505062540}
  m_StartingInteractionOverridesMap:
  - groupMember: {fileID: 2141651114331267770}
    overrideGroupMembers:
    - {fileID: 6045481230268494804}
--- !u!1 &1680501586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1680501587}
  m_Layer: 0
  m_Name: Camera Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1680501587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1680501586}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.36144, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1767192434}
  - {fileID: 2196849375614954873}
  - {fileID: 3595914740002285240}
  - {fileID: 202364688}
  - {fileID: 716906830792148215}
  - {fileID: 1670256625}
  - {fileID: 8718302446126152263}
  m_Father: {fileID: 1717954561962503726}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1767192433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1767192434}
  - component: {fileID: 1767192439}
  - component: {fileID: 1767192437}
  - component: {fileID: 6232745470614056083}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1767192434
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767192433}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1680501587}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!20 &1767192439
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767192433}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.01
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!81 &1767192437
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767192433}
  m_Enabled: 1
--- !u!114 &6232745470614056083
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1767192433}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 0
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 0bacfa51-7938-4a88-adae-9e8ba6c59d23
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings:
      - m_Name: 
        m_Id: f5efb008-b167-4d0f-b9e0-49a2350a85b3
        m_Path: <XRHMD>/centerEyePosition
        m_Interactions: 
        m_Processors: 
        m_Groups: 
        m_Action: Position
        m_Flags: 0
      m_Flags: 0
    m_Reference: {fileID: 7862207684358717888, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 5439f14e-c9da-4bd1-ad3f-7121a75c10d9
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings:
      - m_Name: 
        m_Id: f984a7fd-f7e2-45ef-b21d-699a5d160f29
        m_Path: <XRHMD>/centerEyeRotation
        m_Interactions: 
        m_Processors: 
        m_Groups: 
        m_Action: Rotation
        m_Flags: 0
      m_Flags: 0
    m_Reference: {fileID: -530380113134220495, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State Input
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: be9cc21d-5595-4ea6-aa72-e48652a11968
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 1031966339891076899, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_PositionAction:
    m_Name: Position
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 0bacfa51-7938-4a88-adae-9e8ba6c59d23
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings:
    - m_Name: 
      m_Id: f5efb008-b167-4d0f-b9e0-49a2350a85b3
      m_Path: <XRHMD>/centerEyePosition
      m_Interactions: 
      m_Processors: 
      m_Groups: 
      m_Action: Position
      m_Flags: 0
    m_Flags: 0
  m_RotationAction:
    m_Name: Rotation
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 5439f14e-c9da-4bd1-ad3f-7121a75c10d9
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings:
    - m_Name: 
      m_Id: f984a7fd-f7e2-45ef-b21d-699a5d160f29
      m_Path: <XRHMD>/centerEyeRotation
      m_Interactions: 
      m_Processors: 
      m_Groups: 
      m_Action: Rotation
      m_Flags: 0
    m_Flags: 0
--- !u!1 &58445280694286476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7635210561634702159}
  - component: {fileID: 153982007679157697}
  m_Layer: 2
  m_Name: Move
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7635210561634702159
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 58445280694286476}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6981642495833523204}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &153982007679157697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 58445280694286476}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b1e8c997df241c1a67045eeac79b41b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_MoveSpeed: 1
  m_EnableStrafe: 1
  m_EnableFly: 0
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_ForwardSource: {fileID: 1767192434}
  m_LeftHandMoveAction:
    m_UseReference: 1
    m_Action:
      m_Name: Left Hand Move
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: fa65baa9-ca16-4d92-8425-3195462c2aea
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 6972639530819350904, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RightHandMoveAction:
    m_UseReference: 1
    m_Action:
      m_Name: Right Hand Move
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 7c956f1d-8187-437c-8dc4-afdbf8424b34
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8198699208435500284, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_HeadTransform: {fileID: 1767192434}
  m_LeftControllerTransform: {fileID: 202364688}
  m_RightControllerTransform: {fileID: 1670256625}
  m_LeftHandMovementDirection: 0
  m_RightHandMovementDirection: 0
--- !u!1 &1470279098769358944
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8770899961536015614}
  - component: {fileID: 6397299583031462443}
  - component: {fileID: 3072087220465571386}
  m_Layer: 2
  m_Name: Turn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8770899961536015614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470279098769358944}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6981642495833523204}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6397299583031462443
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470279098769358944}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2213c36610e3b1c4bbf886810ed9db12, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_TurnAmount: 45
  m_DebounceTime: 0.5
  m_EnableTurnLeftRight: 1
  m_EnableTurnAround: 1
  m_DelayTime: 0
  m_LeftHandSnapTurnAction:
    m_UseReference: 1
    m_Action:
      m_Name: Left Hand Snap Turn
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: a1d07c24-ca50-422a-a23f-685d9fabf63b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RightHandSnapTurnAction:
    m_UseReference: 1
    m_Action:
      m_Name: Right Hand Snap Turn
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: a7b54797-6974-4f75-81d0-42b9c15ef1e0
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f, type: 3}
--- !u!114 &3072087220465571386
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470279098769358944}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 919e39492806b334982b6b84c90dd927, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_TurnSpeed: 60
  m_LeftHandTurnAction:
    m_UseReference: 1
    m_Action:
      m_Name: Left Hand Turn
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: d065cb11-e9f6-4747-a3d4-1c032fc345a0
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RightHandTurnAction:
    m_UseReference: 1
    m_Action:
      m_Name: Right Hand Turn
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: e043a43a-0352-4ee2-ab81-9dafdfb41dc2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f, type: 3}
--- !u!1 &1717954561962503725
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717954561962503726}
  - component: {fileID: 1178791450436251564}
  - component: {fileID: 6232745470614056090}
  - component: {fileID: 6232745470614056095}
  - component: {fileID: 5826056641483426609}
  - component: {fileID: 5033801203051696737}
  m_Layer: 2
  m_Name: XR Origin (XR Rig)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717954561962503726
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717954561962503725}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1680501587}
  - {fileID: 6981642495833523204}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1178791450436251564
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717954561962503725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e0cb9aa70a22847b5925ee5f067c10a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Camera: {fileID: 1767192439}
  m_OriginBaseGameObject: {fileID: 1717954561962503725}
  m_CameraFloorOffsetObject: {fileID: 1680501586}
  m_RequestedTrackingOriginMode: 0
  m_CameraYOffset: 1.36144
--- !u!143 &6232745470614056090
CharacterController:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717954561962503725}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Height: 1.36144
  m_Radius: 0.1
  m_SlopeLimit: 45
  m_StepOffset: 0.5
  m_SkinWidth: 0.08
  m_MinMoveDistance: 0.001
  m_Center: {x: 0, y: 0.76072, z: 0}
--- !u!114 &6232745470614056095
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717954561962503725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af6bf904e410ee8479f9093d8830d1f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LocomotionProvider: {fileID: 153982007679157697}
  m_MinHeight: 0
  m_MaxHeight: Infinity
--- !u!114 &5826056641483426609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717954561962503725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 82bc72d2ecc8add47b2fe00d40318500, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LeftHand: {fileID: 0}
  m_RightHand: {fileID: 0}
  m_LeftController: {fileID: 202364687}
  m_RightController: {fileID: **********}
  m_TrackedHandModeStarted:
    m_PersistentCalls:
      m_Calls: []
  m_TrackedHandModeEnded:
    m_PersistentCalls:
      m_Calls: []
  m_MotionControllerModeStarted:
    m_PersistentCalls:
      m_Calls: []
  m_MotionControllerModeEnded:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &5033801203051696737
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717954561962503725}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c9b3d17eeb2e6bc47ada81d8f7f638d8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GazeInteractor: {fileID: 2734315883792958320}
  m_FallbackDivergence: 60
  m_HideCursorWithNoActiveRays: 1
  m_RayInteractors:
  - m_Interactor: {fileID: 285389467476424711}
    m_TeleportRay: 0
  - m_Interactor: {fileID: 1319746312}
    m_TeleportRay: 1
  - m_Interactor: {fileID: 19064736505062540}
    m_TeleportRay: 0
  - m_Interactor: {fileID: 2449787133337329425}
    m_TeleportRay: 1
  m_AimAssistRequiredAngle: 30
  m_AimAssistRequiredSpeed: 0.25
  m_AimAssistPercent: 0.8
  m_AimAssistMaxSpeedPercent: 10
--- !u!1 &1787516059220952802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 150171005766949883}
  m_Layer: 0
  m_Name: Right Controller Stabilized Attach
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &150171005766949883
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1787516059220952802}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8718302446126152263}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2190828208922718286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7401259364726987263}
  - component: {fileID: 1748222016861356527}
  m_Layer: 2
  m_Name: Teleportation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7401259364726987263
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2190828208922718286}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6981642495833523204}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1748222016861356527
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2190828208922718286}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01f69dc1cb084aa42b2f2f8cd87bc770, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_DelayTime: 0
--- !u!1 &2626757739553014894
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8418786636219059989}
  - component: {fileID: 5739245880472075158}
  m_Layer: 2
  m_Name: Climb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8418786636219059989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2626757739553014894}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6981642495833523204}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5739245880472075158
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2626757739553014894}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 496880615cd240be960d436c1c8ae570, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_ClimbSettings:
    m_UseConstant: 1
    m_ConstantValue:
      m_AllowFreeXMovement: 1
      m_AllowFreeYMovement: 1
      m_AllowFreeZMovement: 1
    m_Variable: {fileID: 0}
--- !u!1 &2766569358201078490
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8718302446126152263}
  - component: {fileID: 1801942220539511183}
  m_Layer: 0
  m_Name: Right Controller Stabilized
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8718302446126152263
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2766569358201078490}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 150171005766949883}
  m_Father: {fileID: 1680501587}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1801942220539511183
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2766569358201078490}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 64d299502104b064388841ec2adf6def, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Target: {fileID: 1670256625}
  m_AimTargetObject: {fileID: 19064736505062540}
  m_UseLocalSpace: 1
  m_AngleStabilization: 20
  m_PositionStabilization: 0.25
--- !u!1 &3533369827395663398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3771689589969558132}
  - component: {fileID: 2032798983271290625}
  - component: {fileID: 4083252680172266230}
  - component: {fileID: 742272467831425975}
  m_Layer: 2
  m_Name: Grab Move
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3771689589969558132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3533369827395663398}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6981642495833523204}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2032798983271290625
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3533369827395663398}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b94c4c83dec6a94fbaebf543478259e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_EnableFreeXMovement: 1
  m_EnableFreeYMovement: 0
  m_EnableFreeZMovement: 1
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_ControllerTransform: {fileID: 202364688}
  m_EnableMoveWhileSelecting: 0
  m_MoveFactor: 1
  m_GrabMoveAction:
    m_UseReference: 1
    m_Action:
      m_Name: Grab Move
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 3d33edcf-0043-45cb-95a7-008204badf83
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -3742484312079769484, guid: c348712bda248c246b8c49b3db54643f, type: 3}
--- !u!114 &4083252680172266230
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3533369827395663398}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b94c4c83dec6a94fbaebf543478259e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_EnableFreeXMovement: 1
  m_EnableFreeYMovement: 0
  m_EnableFreeZMovement: 1
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_ControllerTransform: {fileID: 1670256625}
  m_EnableMoveWhileSelecting: 0
  m_MoveFactor: 1
  m_GrabMoveAction:
    m_UseReference: 1
    m_Action:
      m_Name: Grab Move
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: de56d195-bf90-4347-9982-6bf8ffa3420c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 15759602096507913, guid: c348712bda248c246b8c49b3db54643f, type: 3}
--- !u!114 &742272467831425975
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3533369827395663398}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 760ff70c1c91bdd45907d0ff0cdcaf7f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_System: {fileID: 1589979491555116479}
  m_EnableFreeXMovement: 1
  m_EnableFreeYMovement: 0
  m_EnableFreeZMovement: 1
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_LeftGrabMoveProvider: {fileID: 2032798983271290625}
  m_RightGrabMoveProvider: {fileID: 4083252680172266230}
  m_OverrideSharedSettingsOnInit: 1
  m_MoveFactor: 1
  m_RequireTwoHandsForTranslation: 0
  m_EnableRotation: 1
  m_EnableScaling: 0
  m_MinimumScale: 0.2
  m_MaximumScale: 5
--- !u!1 &4026763789982141574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5556032914392612086}
  m_Layer: 0
  m_Name: Left Controller Stabilized Attach
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5556032914392612086
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4026763789982141574}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 716906830792148215}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5167925059111895691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6981642495833523204}
  - component: {fileID: 1589979491555116479}
  m_Layer: 2
  m_Name: Locomotion System
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6981642495833523204
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5167925059111895691}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8770899961536015614}
  - {fileID: 7635210561634702159}
  - {fileID: 3771689589969558132}
  - {fileID: 7401259364726987263}
  - {fileID: 8418786636219059989}
  m_Father: {fileID: 1717954561962503726}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1589979491555116479
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5167925059111895691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 03a5df2202a8b96488c744be3bd0c33e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Timeout: 10
  m_XROrigin: {fileID: 1178791450436251564}
--- !u!1 &5563199296126487199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 716906830792148215}
  - component: {fileID: 3752199730057449385}
  m_Layer: 0
  m_Name: Left Controller Stabilized
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &716906830792148215
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5563199296126487199}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5556032914392612086}
  m_Father: {fileID: 1680501587}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3752199730057449385
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5563199296126487199}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 64d299502104b064388841ec2adf6def, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Target: {fileID: 202364688}
  m_AimTargetObject: {fileID: 285389467476424711}
  m_UseLocalSpace: 1
  m_AngleStabilization: 20
  m_PositionStabilization: 0.25
--- !u!1 &6501755809687671949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3595914740002285240}
  - component: {fileID: 9068281059075228377}
  m_Layer: 0
  m_Name: Gaze Stabilized
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3595914740002285240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6501755809687671949}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8381546940850731792}
  m_Father: {fileID: 1680501587}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9068281059075228377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6501755809687671949}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 64d299502104b064388841ec2adf6def, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Target: {fileID: 2196849375614954873}
  m_AimTargetObject: {fileID: 0}
  m_UseLocalSpace: 1
  m_AngleStabilization: 20
  m_PositionStabilization: 0.25
--- !u!1 &6553456492286146741
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8381546940850731792}
  m_Layer: 0
  m_Name: Gaze Stabilized Attach
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8381546940850731792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6553456492286146741}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3595914740002285240}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &553018692727262454
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1670256625}
    m_Modifications:
    - target: {fileID: 2761784063978902503, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_AttachTransform
      value: 
      objectReference: {fileID: 150171005766949883}
    - target: {fileID: 2761784063978902503, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_RayOriginTransform
      value: 
      objectReference: {fileID: 8718302446126152263}
    - target: {fileID: 2761784063978902504, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_Parameters.numCapVertices
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902504, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_Parameters.numCornerVertices
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902505, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LineWidth
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902505, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LineOriginTransform
      value: 
      objectReference: {fileID: 1670256625}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.035
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902507, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_Name
      value: Teleport Interactor
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c1800acf6366418a9b5f610249000331, type: 3}
--- !u!114 &2449787133337329425 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2761784063978902503, guid: c1800acf6366418a9b5f610249000331, type: 3}
  m_PrefabInstance: {fileID: 553018692727262454}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2449787133337329437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &2449787133337329436 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
  m_PrefabInstance: {fileID: 553018692727262454}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2449787133337329437 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2761784063978902507, guid: c1800acf6366418a9b5f610249000331, type: 3}
  m_PrefabInstance: {fileID: 553018692727262454}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &4898442741215327232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2449787133337329437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UpdateTrackingType: 0
  m_EnableInputTracking: 0
  m_EnableInputActions: 1
  m_ModelPrefab: {fileID: 0}
  m_ModelParent: {fileID: 0}
  m_Model: {fileID: 0}
  m_AnimateModel: 0
  m_ModelSelectTransition: 
  m_ModelDeSelectTransition: 
  m_PositionAction:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: db83b55c-ddbb-4078-919c-0b0cbc6afe9b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_RotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 934a28ac-b7af-4a72-896e-f98ee8741de7
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_IsTrackedAction:
    m_UseReference: 1
    m_Action:
      m_Name: Is Tracked
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: aa4d4d88-f823-41b6-9d48-77b617f86edd
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 1
    m_Reference: {fileID: 0}
  m_TrackingStateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 41ec62b5-77e9-433d-839f-2daf5c703355
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_SelectAction:
    m_UseReference: 1
    m_Action:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 00f93d2a-0364-4d11-90a6-5ef13b9957a8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8270564778575511633, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SelectActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Select Action Value
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 27191759-33e4-4f45-a0fd-7623410a9e62
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8270564778575511633, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: f4128915-c017-4646-8498-38077fd94651
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 83097790271614945, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Activate Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: a7069e58-5c7c-41cc-b6c2-68810e4a9e99
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIPressAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 90782e27-0ef9-4a5a-a0a0-f5b588d6ff72
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_UIPressActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: c27db6a8-7713-4ef9-9c9d-18b76c3a4c97
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_UIScrollAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: e7a23052-7dfc-481a-8bff-2dae62df0f5d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_HapticDeviceAction:
    m_UseReference: 1
    m_Action:
      m_Name: Haptic Device
      m_Type: 2
      m_ExpectedControlType: 
      m_Id: 59ea1b94-e9f8-4049-ab97-5920b11143a5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8222252007134549311, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: Rotate Anchor
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 219ef40d-4838-4ec7-8534-38c5e8e5612d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5913262927076077117, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_DirectionalAnchorRotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Directional Anchor Rotation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 37b7c356-f2e9-47c7-ba4c-34757c8b8df8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -440298646266941818, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TranslateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: Translate Anchor
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: b1006d10-a3ef-4a91-ae37-8d5547fe6331
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_ScaleToggleAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 9a064928-c984-4fe4-a3c8-5ac507a62c67
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_ScaleDeltaAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Delta
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 3e88ad80-3f35-49c8-b51b-bd3b2dac1749
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_ButtonPressPoint: 0.5
--- !u!1001 &2147063422107175346
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 202364688}
    m_Modifications:
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4125421792874400280, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_Name
      value: Poke Interactor
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.005
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9952465
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.04345342
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.08707283
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.0038016832
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 10
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
--- !u!4 &1666320186578454293 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
  m_PrefabInstance: {fileID: 2147063422107175346}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &4343660526480754339 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2417358720014700305, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
  m_PrefabInstance: {fileID: 2147063422107175346}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0924bcaa9eb50df458a783ae0e2b59f5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &2761784064811051247
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 202364688}
    m_Modifications:
    - target: {fileID: 2761784063978902503, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_AttachTransform
      value: 
      objectReference: {fileID: 5556032914392612086}
    - target: {fileID: 2761784063978902503, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_RayOriginTransform
      value: 
      objectReference: {fileID: 716906830792148215}
    - target: {fileID: 2761784063978902504, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_Parameters.numCapVertices
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902504, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_Parameters.numCornerVertices
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902505, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LineWidth
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902505, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LineOriginTransform
      value: 
      objectReference: {fileID: 202364688}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.035
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2761784063978902507, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_Name
      value: Teleport Interactor
      objectReference: {fileID: 0}
    - target: {fileID: 3448426214904589657, guid: c1800acf6366418a9b5f610249000331, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c1800acf6366418a9b5f610249000331, type: 3}
--- !u!1 &1319746308 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2761784063978902507, guid: c1800acf6366418a9b5f610249000331, type: 3}
  m_PrefabInstance: {fileID: 2761784064811051247}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &3114443105980813935
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1319746308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UpdateTrackingType: 0
  m_EnableInputTracking: 0
  m_EnableInputActions: 1
  m_ModelPrefab: {fileID: 0}
  m_ModelParent: {fileID: 0}
  m_Model: {fileID: 0}
  m_AnimateModel: 0
  m_ModelSelectTransition: 
  m_ModelDeSelectTransition: 
  m_PositionAction:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: 8b170a9b-132e-486d-947e-6a244d4362ea
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_RotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 080819c2-8547-4beb-8522-e6356be16fb1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_IsTrackedAction:
    m_UseReference: 1
    m_Action:
      m_Name: Is Tracked
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 3975349d-0575-4146-938e-7c8de713073b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 1
    m_Reference: {fileID: 0}
  m_TrackingStateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 8db27cb3-9369-4e6a-9c66-17df490c89af
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_SelectAction:
    m_UseReference: 1
    m_Action:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 8e000d1c-13a4-4cc0-ad37-f2e125874399
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -4084014799535200556, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SelectActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Select Action Value
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 07d9e458-3e59-4acb-8a8f-2325c5fb0904
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -4084014799535200556, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateAction:
    m_UseReference: 1
    m_Action:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: 3995f9f4-6aa7-409a-80d2-5f7ea1464fde
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5982496924579745919, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ActivateActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: Activate Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: b99c6f8c-3762-478a-b2d6-882b9175d1bb
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIPressAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: Button
      m_Id: db89d01c-df6f-4954-b868-103dd5bdb514
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_UIPressActionValue:
    m_UseReference: 1
    m_Action:
      m_Name: UI Press Action Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 15e220ae-aa18-4d3d-a3d7-0c505dc649c8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_UIScrollAction:
    m_UseReference: 1
    m_Action:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: c2cad5a5-b96a-4161-ac52-0781e2861c1e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_HapticDeviceAction:
    m_UseReference: 1
    m_Action:
      m_Name: Haptic Device
      m_Type: 2
      m_ExpectedControlType: 
      m_Id: 3e09b626-c80d-40ec-9592-eb3fe89c2038
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8785819595477538065, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_RotateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: Rotate Anchor
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 3dca8766-e652-4e78-8406-420aa73ba338
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -7363382999065477798, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_DirectionalAnchorRotationAction:
    m_UseReference: 1
    m_Action:
      m_Name: Directional Anchor Rotation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 6c0b9ec3-aa4f-4e6a-85ea-7db994c51246
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -8811388872089202044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_TranslateAnchorAction:
    m_UseReference: 1
    m_Action:
      m_Name: Translate Anchor
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: e873605e-6a95-4389-8fbe-39069340ba92
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_ScaleToggleAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 8ade6c1d-9712-4906-b8c1-b614dad5da11
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_ScaleDeltaAction:
    m_UseReference: 1
    m_Action:
      m_Name: Scale Delta
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 6317d004-7d4a-4ee3-b309-c53cf4879474
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  m_ButtonPressPoint: 0.5
--- !u!4 &1319746309 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2761784063978902506, guid: c1800acf6366418a9b5f610249000331, type: 3}
  m_PrefabInstance: {fileID: 2761784064811051247}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1319746312 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2761784063978902503, guid: c1800acf6366418a9b5f610249000331, type: 3}
  m_PrefabInstance: {fileID: 2761784064811051247}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1319746308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &2950107899174535523
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 202364688}
    m_Modifications:
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8841706926471734270, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_Name
      value: Direct Interactor
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
--- !u!114 &2434299456458490401 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 664489801923019586, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
  m_PrefabInstance: {fileID: 2950107899174535523}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4253f32900bcc4d499d675566142ded0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &9013359448673381486 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
  m_PrefabInstance: {fileID: 2950107899174535523}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4338235989863673259
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1670256625}
    m_Modifications:
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4125421792874400280, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_Name
      value: Poke Interactor
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.005
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9952465
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.04345342
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.08707283
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.0038016832
      objectReference: {fileID: 0}
    - target: {fileID: 8259524632637961923, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -10
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
--- !u!114 &2141651114331267770 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2417358720014700305, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
  m_PrefabInstance: {fileID: 4338235989863673259}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0924bcaa9eb50df458a783ae0e2b59f5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &3954319948395782924 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 780270278251679399, guid: 27024f5809f4a4347b9cd7f26a1bdf93, type: 3}
  m_PrefabInstance: {fileID: 4338235989863673259}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6547495889434062998
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1670256625}
    m_Modifications:
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8841706926471734270, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
      propertyPath: m_Name
      value: Direct Interactor
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
--- !u!4 &1096734238491090331 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6189354538098044173, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
  m_PrefabInstance: {fileID: 6547495889434062998}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &6045481230268494804 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 664489801923019586, guid: 2fd3e07afe5b461490fb8e314976b1b0, type: 3}
  m_PrefabInstance: {fileID: 6547495889434062998}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4253f32900bcc4d499d675566142ded0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &7234066737556534056
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 202364688}
    m_Modifications:
    - target: {fileID: 1787346994484839025, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_Name
      value: Ray Interactor
      objectReference: {fileID: 0}
    - target: {fileID: 4924506573850889901, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LineOriginTransform
      value: 
      objectReference: {fileID: 202364688}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.035
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7462879561657043759, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_AttachTransform
      value: 
      objectReference: {fileID: 5556032914392612086}
    - target: {fileID: 7462879561657043759, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_RayOriginTransform
      value: 
      objectReference: {fileID: 716906830792148215}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: ad818c36731146e994540a1896ad8f24, type: 3}
--- !u!114 &285389467476424711 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7462879561657043759, guid: ad818c36731146e994540a1896ad8f24, type: 3}
  m_PrefabInstance: {fileID: 7234066737556534056}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &3881409846907809176 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
  m_PrefabInstance: {fileID: 7234066737556534056}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7481255429629652899
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1670256625}
    m_Modifications:
    - target: {fileID: 1787346994484839025, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_Name
      value: Ray Interactor
      objectReference: {fileID: 0}
    - target: {fileID: 4924506573850889901, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LineOriginTransform
      value: 
      objectReference: {fileID: 1670256625}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.02
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.035
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7462879561657043759, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_AttachTransform
      value: 
      objectReference: {fileID: 150171005766949883}
    - target: {fileID: 7462879561657043759, guid: ad818c36731146e994540a1896ad8f24, type: 3}
      propertyPath: m_RayOriginTransform
      value: 
      objectReference: {fileID: 8718302446126152263}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: ad818c36731146e994540a1896ad8f24, type: 3}
--- !u!114 &19064736505062540 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7462879561657043759, guid: ad818c36731146e994540a1896ad8f24, type: 3}
  m_PrefabInstance: {fileID: 7481255429629652899}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &3921468432820449555 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5888765399538998960, guid: ad818c36731146e994540a1896ad8f24, type: 3}
  m_PrefabInstance: {fileID: 7481255429629652899}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8654467957078447927
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1680501587}
    m_Modifications:
    - target: {fileID: 3055433562365713971, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_Name
      value: Gaze Interactor
      objectReference: {fileID: 0}
    - target: {fileID: 3055433562365713971, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6766910295942714439, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_AttachTransform
      value: 
      objectReference: {fileID: 8381546940850731792}
    - target: {fileID: 6766910295942714439, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_RayOriginTransform
      value: 
      objectReference: {fileID: 3595914740002285240}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
--- !u!4 &2196849375614954873 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7378618157167557198, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
  m_PrefabInstance: {fileID: 8654467957078447927}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2734315883792958320 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6766910295942714439, guid: b84cd05e1160fe34cab2585022c8cd99, type: 3}
  m_PrefabInstance: {fileID: 8654467957078447927}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c416f1a5c494e224fb5564fd1362b50d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
