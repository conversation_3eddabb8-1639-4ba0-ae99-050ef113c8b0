{"name": "XR Device Simulator Controls", "maps": [{"name": "Main", "id": "c96c4ddb-3eb8-4074-bbd9-a8ae6f1f6475", "actions": [{"name": "Keyboard X Translate", "type": "Value", "id": "d4eb7006-5077-4816-9d5c-f570b6d586f3", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Keyboard Z Translate", "type": "Value", "id": "3ea275ac-e111-4610-891f-105676c72cd5", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Keyboard Y Translate", "type": "Value", "id": "5cc58f95-e9dc-4675-a42e-dd66874c3ba3", "expectedControlType": "Axis", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Toggle Manipulate Left", "type": "<PERSON><PERSON>", "id": "847b79d9-a69b-4484-8688-a4bf40e58163", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Manipulate Right", "type": "<PERSON><PERSON>", "id": "241f6068-ebc8-4c6d-b747-8bc2c1f74f87", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Manipulate Body", "type": "<PERSON><PERSON>", "id": "c81093ea-c17e-4430-a3df-bccabef74af4", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Manipulate Left", "type": "<PERSON><PERSON>", "id": "07c46cc4-c35d-4364-a878-68fad8ab8c64", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Manipulate Right", "type": "<PERSON><PERSON>", "id": "307cb608-f32e-48a3-8ce6-d1cd83a5fb90", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Manipulate Head", "type": "<PERSON><PERSON>", "id": "f5febf74-651b-4f73-8d0a-08b0acdabc4d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Hand-Controller Mode", "type": "<PERSON><PERSON>", "id": "31a82fde-55bc-4b18-b15a-8adc7e75658e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cycle Devices", "type": "<PERSON><PERSON>", "id": "d728c6fb-4deb-4268-9110-d64c7861cd17", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Stop Manipulation", "type": "<PERSON><PERSON>", "id": "974ae49e-da4e-4dc8-a6be-cb63986d8f8e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Mouse Delta", "type": "Value", "id": "0b945dbf-d750-40cb-97c6-593686fcf012", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "<PERSON>", "type": "Value", "id": "b2a408da-a9fd-4638-9af3-17fb9bc2811d", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Rotate Mode Override", "type": "<PERSON><PERSON>", "id": "2e390909-c0f6-4ca5-b8bc-4c54090f96d9", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Mouse Transformation Mode", "type": "<PERSON><PERSON>", "id": "ae2b5c7f-b5e0-4b93-b674-172de9f68380", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Negate Mode", "type": "<PERSON><PERSON>", "id": "8c837143-e018-41f0-9e0e-907acb9d7360", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Z Constraint", "type": "<PERSON><PERSON>", "id": "d3e9308c-6f8c-46f7-bb6f-14422c345983", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "X Constraint", "type": "<PERSON><PERSON>", "id": "11dc7a94-7230-49ff-b56d-06e6473e9951", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Y Constraint", "type": "<PERSON><PERSON>", "id": "11ab79c6-b9c6-4301-8086-3e9c6904ef14", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Reset", "type": "<PERSON><PERSON>", "id": "339ccb79-aee9-4ba4-8864-3b6c81c199db", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Cursor Lock", "type": "<PERSON><PERSON>", "id": "9bd36ab1-f676-4ff4-8a4d-ba0c6fb36268", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Primary 2D Axis Target", "type": "<PERSON><PERSON>", "id": "e0fdec2d-309b-4313-aad7-9dcc71f1394d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Secondary 2D Axis Target", "type": "<PERSON><PERSON>", "id": "b3b49ea5-f80f-4d24-a782-d61a13a004b3", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Toggle Device Position Target", "type": "<PERSON><PERSON>", "id": "15cd3c4a-56b1-4a43-a924-a2118e2adaf4", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "1D Axis", "id": "db741065-2a46-439d-9e13-11960dc3355a", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Keyboard X Translate", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "3d2e07de-025e-4c2e-98df-250511a8ff6d", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "", "action": "Keyboard X Translate", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "04e1437d-c862-4a04-8f8e-40e9f52c4f5e", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "", "action": "Keyboard X Translate", "isComposite": false, "isPartOfComposite": true}, {"name": "1D Axis", "id": "cb7c4679-31f4-4170-885a-e7d78c049443", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Keyboard Z Translate", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "732e790c-f2a2-4f90-b525-53139a358de7", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "", "action": "Keyboard Z Translate", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "d5a98916-ade7-419d-a138-86bcdf05670f", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "", "action": "Keyboard Z Translate", "isComposite": false, "isPartOfComposite": true}, {"name": "1D Axis", "id": "fca20498-bf6f-4824-ba15-6dc2d191eb2f", "path": "1DAxis", "interactions": "", "processors": "", "groups": "", "action": "Keyboard Y Translate", "isComposite": true, "isPartOfComposite": false}, {"name": "negative", "id": "2c01790c-a24a-4266-a2e9-74e2a1ca3fa7", "path": "<Keyboard>/q", "interactions": "", "processors": "", "groups": "", "action": "Keyboard Y Translate", "isComposite": false, "isPartOfComposite": true}, {"name": "positive", "id": "f5223aed-93c0-4633-8aa3-c393ce890872", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "", "action": "Keyboard Y Translate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "fcfb16fb-edee-474c-b1f2-f10b2a0a3569", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "", "action": "Manipulate Left", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e791caef-78d3-4f68-9104-212f73ac0642", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "", "action": "Manipulate Right", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b2c1b1cd-6ea7-45b6-b68f-17b3662b4d3a", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": "", "action": "Mouse Delta", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "66e536bc-b5b6-4c7b-903a-fbcc05fc854e", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": "", "action": "<PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2b070a4a-e044-4cbd-a8e2-6b362785bf21", "path": "<Keyboard>/leftCtrl", "interactions": "", "processors": "", "groups": "", "action": "Rotate Mode Override", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0fa0d566-1e07-4e17-9b14-3e8fce69ec26", "path": "<Keyboard>/x", "interactions": "", "processors": "", "groups": "", "action": "X Constraint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "71c185e1-73fb-4691-b910-70610f397b42", "path": "<Keyboard>/c", "interactions": "", "processors": "", "groups": "", "action": "Y Constraint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "09809c10-d09e-4c49-b58f-1995e50cf685", "path": "<Keyboard>/z", "interactions": "", "processors": "", "groups": "", "action": "Z Constraint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7611d6eb-0ff4-431f-998d-6fa429e0e1e1", "path": "<Keyboard>/r", "interactions": "", "processors": "", "groups": "", "action": "Toggle Mouse Transformation Mode", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5c889b78-f9fd-4cd7-96dd-5399428f6992", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": "", "action": "Negate Mode", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "85f27bdb-dfe8-48d3-8512-205b3ad6306a", "path": "<Keyboard>/backslash", "interactions": "", "processors": "", "groups": "", "action": "Toggle Cursor Lock", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3542472e-e883-407a-b967-5b879b2d7dc4", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "", "action": "Manipulate Head", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "cd7dcdd6-b569-4c25-87ea-c62a0fb1cf89", "path": "<Keyboard>/v", "interactions": "", "processors": "", "groups": "", "action": "Reset", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4bfdd0e6-1936-4f44-8e97-20e16dbc879f", "path": "<Keyboard>/1", "interactions": "", "processors": "", "groups": "", "action": "Toggle Primary 2D Axis Target", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "81d47d9e-4920-4098-94d8-bac2888d6433", "path": "<Keyboard>/2", "interactions": "", "processors": "", "groups": "", "action": "Toggle Secondary 2D Axis Target", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9404bec7-672a-4fb1-adb3-e2dc4e32801b", "path": "<Keyboard>/3", "interactions": "", "processors": "", "groups": "", "action": "Toggle Device Position Target", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5dacc4c7-2e70-4500-8c72-99595c72b49e", "path": "<Keyboard>/tab", "interactions": "", "processors": "", "groups": "", "action": "Cycle Devices", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "84760c74-7de6-46dd-a097-3bc198cf63cd", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "", "action": "Stop Manipulation", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e8a0a3b9-06cf-40a8-86d8-1d8f1d704451", "path": "<Keyboard>/y", "interactions": "", "processors": "", "groups": "", "action": "Toggle Manipulate Right", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b62df009-54c9-4b03-9721-07ca66fe1bdf", "path": "<Keyboard>/t", "interactions": "", "processors": "", "groups": "", "action": "Toggle Manipulate Left", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "51f71c9d-9024-4bdd-8ea6-19b987e261b1", "path": "<Keyboard>/u", "interactions": "", "processors": "", "groups": "", "action": "Toggle Manipulate Body", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d7d20173-13ae-4c9f-b6a9-bf8a89517d6a", "path": "<Keyboard>/h", "interactions": "", "processors": "", "groups": "", "action": "Hand-Controller Mode", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": []}