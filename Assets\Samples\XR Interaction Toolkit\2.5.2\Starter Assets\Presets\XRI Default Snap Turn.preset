%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Snap Turn
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: 2213c36610e3b1c4bbf886810ed9db12, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_System
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TurnAmount
    value: 45
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DebounceTime
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableTurnLeftRight
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableTurnAround
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DelayTime
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_Name
    value: Left Hand Snap Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_Id
    value: a1d07c24-ca50-422a-a23f-685d9fabf63b
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandSnapTurnAction.m_Reference
    value: 
    objectReference: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_Name
    value: Right Hand Snap Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_Id
    value: a7b54797-6974-4f75-81d0-42b9c15ef1e0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandSnapTurnAction.m_Reference
    value: 
    objectReference: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ExcludedProperties: []
