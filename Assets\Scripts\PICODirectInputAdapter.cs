using UnityEngine;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// PICO直接输入适配器
/// 直接从XRController读取输入状态，不依赖Input Action系统
/// </summary>
public class PICODirectInputAdapter : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;
    
    [Header("输入设置")]
    [SerializeField] private bool enableVRInput = true;
    [SerializeField] private bool showInputDebug = true;
    [SerializeField] private float inputCooldown = 0.5f;
    
    [Header("备用键盘输入")]
    [SerializeField] private bool enableKeyboardFallback = true;
    [SerializeField] private KeyCode fallbackTriggerKey = KeyCode.T;
    [SerializeField] private KeyCode fallbackPrimaryKey = KeyCode.P;
    [SerializeField] private KeyCode fallbackSecondaryKey = KeyCode.G;
    [SerializeField] private KeyCode fallbackMenuKey = KeyCode.F1;

    // 控制器引用
#if UNITY_XR_INTERACTION_TOOLKIT
    private XRController leftController;
    private XRController rightController;
#endif

    // 输入状态跟踪
    private bool lastTriggerState = false;
    private bool lastPrimaryState = false;
    private bool lastSecondaryState = false;
    private bool lastMenuState = false;
    private float lastInputTime = 0f;

    void Start()
    {
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }
        
        InitializeControllers();
        Debug.Log("[PICODirectInputAdapter] PICO直接输入适配器已启动");
        ShowInputMapping();
    }

    void Update()
    {
        if (enableVRInput)
        {
            HandleVRInput();
        }
        
        if (enableKeyboardFallback)
        {
            HandleKeyboardInput();
        }
    }

    /// <summary>
    /// 初始化控制器
    /// </summary>
    private void InitializeControllers()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        var controllers = FindObjectsOfType<XRController>();
        foreach (var controller in controllers)
        {
            if (controller.controllerNode == XRNode.LeftHand)
            {
                leftController = controller;
                Debug.Log("[PICODirectInputAdapter] ✓ 找到左手控制器");
            }
            else if (controller.controllerNode == XRNode.RightHand)
            {
                rightController = controller;
                Debug.Log("[PICODirectInputAdapter] ✓ 找到右手控制器");
            }
        }
        
        if (leftController == null && rightController == null)
        {
            Debug.LogWarning("[PICODirectInputAdapter] ⚠ 未找到任何VR控制器");
        }
#endif
    }

    /// <summary>
    /// 处理VR输入
    /// </summary>
    private void HandleVRInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 优先使用右手控制器
        if (rightController != null && rightController.inputDevice.isValid)
        {
            CheckControllerInput(rightController, "右手");
        }
        // 如果右手控制器不可用，使用左手控制器
        else if (leftController != null && leftController.inputDevice.isValid)
        {
            CheckControllerInput(leftController, "左手");
        }
#endif
    }

    /// <summary>
    /// 检查控制器输入
    /// </summary>
    private void CheckControllerInput(object controllerObj, string handName)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        var controller = controllerObj as XRController;
        if (controller == null || !controller.inputDevice.isValid) return;

        var inputDevice = controller.inputDevice;
        
        // 获取当前按钮状态
        bool triggerPressed = false;
        bool primaryPressed = false;
        bool secondaryPressed = false;
        bool menuPressed = false;
        
        inputDevice.TryGetFeatureValue(CommonUsages.triggerButton, out triggerPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.primaryButton, out primaryPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.secondaryButton, out secondaryPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.menuButton, out menuPressed);
        
        // 检测按钮按下事件（边缘检测）
        if (triggerPressed && !lastTriggerState && CanProcessInput())
        {
            OnTriggerPressed(handName);
        }
        
        if (primaryPressed && !lastPrimaryState && CanProcessInput())
        {
            OnPrimaryButtonPressed(handName);
        }
        
        if (secondaryPressed && !lastSecondaryState && CanProcessInput())
        {
            OnSecondaryButtonPressed(handName);
        }
        
        if (menuPressed && !lastMenuState && CanProcessInput())
        {
            OnMenuButtonPressed(handName);
        }
        
        // 更新状态
        lastTriggerState = triggerPressed;
        lastPrimaryState = primaryPressed;
        lastSecondaryState = secondaryPressed;
        lastMenuState = menuPressed;
        
        // 显示输入调试信息
        if (showInputDebug && (triggerPressed || primaryPressed || secondaryPressed || menuPressed))
        {
            Debug.Log($"[PICODirectInputAdapter] {handName}控制器输入 - T:{triggerPressed} P:{primaryPressed} S:{secondaryPressed} M:{menuPressed}");
        }
#endif
    }

    /// <summary>
    /// 处理键盘输入（备用）
    /// </summary>
    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(fallbackTriggerKey) && CanProcessInput())
        {
            OnTriggerPressed("键盘");
        }
        
        if (Input.GetKeyDown(fallbackPrimaryKey) && CanProcessInput())
        {
            OnPrimaryButtonPressed("键盘");
        }
        
        if (Input.GetKeyDown(fallbackSecondaryKey) && CanProcessInput())
        {
            OnSecondaryButtonPressed("键盘");
        }
        
        if (Input.GetKeyDown(fallbackMenuKey) && CanProcessInput())
        {
            OnMenuButtonPressed("键盘");
        }
    }

    /// <summary>
    /// 检查是否可以处理输入
    /// </summary>
    private bool CanProcessInput()
    {
        if (Time.time - lastInputTime < inputCooldown)
        {
            return false;
        }
        
        lastInputTime = Time.time;
        return true;
    }

    /// <summary>
    /// 扳机键按下事件
    /// </summary>
    private void OnTriggerPressed(string inputSource)
    {
        Debug.Log($"[PICODirectInputAdapter] {inputSource}扳机键按下 - 执行第一部分功能：装配区域移动到基于摄像机的固定位置");
        ExecuteFirstPartFunction();
    }

    /// <summary>
    /// 主按钮按下事件
    /// </summary>
    private void OnPrimaryButtonPressed(string inputSource)
    {
        Debug.Log($"[PICODirectInputAdapter] {inputSource}主按钮按下 - 执行第二部分功能：让装配面朝向摄像机");
        ExecuteSecondPartFunction();
    }

    /// <summary>
    /// 副按钮按下事件
    /// </summary>
    private void OnSecondaryButtonPressed(string inputSource)
    {
        Debug.Log($"[PICODirectInputAdapter] {inputSource}副按钮按下 - 重置装配区域位置");
        ExecuteResetFunction();
    }

    /// <summary>
    /// 菜单按钮按下事件
    /// </summary>
    private void OnMenuButtonPressed(string inputSource)
    {
        Debug.Log($"[PICODirectInputAdapter] {inputSource}菜单按钮按下 - 切换调试界面");
        ExecuteToggleDebugFunction();
    }

    /// <summary>
    /// 执行第一部分功能
    /// </summary>
    private void ExecuteFirstPartFunction()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICODirectInputAdapter] VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.TestCameraBasedPositioning();
    }

    /// <summary>
    /// 执行第二部分功能
    /// </summary>
    private void ExecuteSecondPartFunction()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICODirectInputAdapter] VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.TestOrientation();
    }

    /// <summary>
    /// 执行重置功能
    /// </summary>
    private void ExecuteResetFunction()
    {
        if (debugger == null)
        {
            Debug.LogError("[PICODirectInputAdapter] VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.ResetPosition();
    }

    /// <summary>
    /// 执行切换调试界面功能
    /// </summary>
    private void ExecuteToggleDebugFunction()
    {
        Debug.Log("[PICODirectInputAdapter] 切换调试界面功能");
        // 这里可以添加具体的调试界面切换逻辑
    }

    /// <summary>
    /// 显示输入映射
    /// </summary>
    [ContextMenu("显示输入映射")]
    public void ShowInputMapping()
    {
        string mapping = @"
PICODirectInputAdapter 输入映射：

VR控制器输入（直接读取）：
- 扳机键 (Trigger) → 第一部分功能：装配区域移动到基于摄像机的固定位置
- 主按钮 (A/X) → 第二部分功能：让装配面朝向摄像机
- 副按钮 (B/Y) → 重置装配区域位置
- 菜单按钮 (Menu) → 切换调试界面

键盘备用输入：
- T键 → 第一部分功能
- P键 → 第二部分功能
- G键 → 重置功能
- F1键 → 切换调试界面

特点：
- 直接从XRController读取输入状态
- 不依赖Input Action系统
- 使用边缘检测防止重复触发
- 有输入冷却时间保护
";
        Debug.Log(mapping);
    }

    /// <summary>
    /// 手动测试控制器状态
    /// </summary>
    [ContextMenu("测试控制器状态")]
    public void TestControllerStatus()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        Debug.Log("=== 控制器状态测试 ===");
        
        if (rightController != null)
        {
            Debug.Log($"右手控制器: {rightController.name}");
            Debug.Log($"设备有效: {rightController.inputDevice.isValid}");
            if (rightController.inputDevice.isValid)
            {
                Debug.Log($"设备名称: {rightController.inputDevice.name}");
            }
        }
        
        if (leftController != null)
        {
            Debug.Log($"左手控制器: {leftController.name}");
            Debug.Log($"设备有效: {leftController.inputDevice.isValid}");
            if (leftController.inputDevice.isValid)
            {
                Debug.Log($"设备名称: {leftController.inputDevice.name}");
            }
        }
#endif
    }
}
