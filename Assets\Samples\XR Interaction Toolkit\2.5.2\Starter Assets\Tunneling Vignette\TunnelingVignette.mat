%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TunnelingVignette
  m_Shader: {fileID: 4800000, guid: e51b4af1e50be764e8de46e07d4e3f3f, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _WINDQUALITY_NONE
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Control:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ExtraTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Splat3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TerrainHolesTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Vector1_3F06E706: 0.2
    - Vector1_75dd1e4765a74fbfb568941f1cbc3b28: 0.2
    - Vector1_EA51145: 0.7
    - Vector1_b81b3127f28249bf9aa125c543e5fa41: 0.591
    - _ApertureSize: 1
    - _BillboardKwToggle: 0
    - _BillboardShadowFade: 0.5
    - _ColorMask: 15
    - _EnableHeightBlend: 0
    - _EnableInstancedPerPixelNormal: 1
    - _FeatheringEffect: 0
    - _Glossiness: 0.5
    - _HeightTransition: 0
    - _HueVariationKwToggle: 0
    - _Metallic: 0
    - _Metallic0: 0
    - _Metallic1: 0
    - _Metallic2: 0
    - _Metallic3: 0
    - _NormalMapKwToggle: 0
    - _NumLayersCount: 1
    - _Smoothness0: 0.5
    - _Smoothness1: 0.5
    - _Smoothness2: 0.5
    - _Smoothness3: 0.5
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _SubsurfaceIndirect: 0.25
    - _SubsurfaceKwToggle: 0
    - _TwoSided: 2
    - _UseUIAlphaClip: 0
    - _WindQuality: 0
    m_Colors:
    - Color_289bc150a04c4e18b064c9c73585a3e6: {r: 0, g: 0, b: 0, a: 1}
    - Color_8A2FD431: {r: 0, g: 0, b: 0, a: 0}
    - Color_9DC7CF27: {r: 0, g: 0, b: 0, a: 0}
    - Color_c07642b1f5ef4fdba00f1bb21dbcab55: {r: 0, g: 0, b: 0, a: 1}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _HueVariationColor: {r: 1, g: 0.5, b: 0, a: 0.1}
    - _SubsurfaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _VignetteColor: {r: 0, g: 0, b: 0, a: 1}
    - _VignetteColorBlend: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &8502541265190943013
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 4
