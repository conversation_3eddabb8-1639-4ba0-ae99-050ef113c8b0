﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.Networking.UnityWebRequest UnityEngine.Networking.UnityWebRequestTexture::GetTexture(System.String)
extern void UnityWebRequestTexture_GetTexture_mEEC74BBC5190D4012A0B87C5D439AF59E22EF2EF (void);
// 0x00000002 UnityEngine.Networking.UnityWebRequest UnityEngine.Networking.UnityWebRequestTexture::GetTexture(System.String,System.Boolean)
extern void UnityWebRequestTexture_GetTexture_m45F855106C834021AC0DFA25FE31BA14C42693CA (void);
// 0x00000003 System.IntPtr UnityEngine.Networking.DownloadHandlerTexture::Create(UnityEngine.Networking.DownloadHandlerTexture,System.Boolean)
extern void DownloadHandlerTexture_Create_m6DBB03DC64DB11F35862D7A4F6B4A814B8531A21 (void);
// 0x00000004 System.Void UnityEngine.Networking.DownloadHandlerTexture::InternalCreateTexture(System.Boolean)
extern void DownloadHandlerTexture_InternalCreateTexture_m2BC0B138561A55B9CCAB694E4447888B21D1B47A (void);
// 0x00000005 System.Void UnityEngine.Networking.DownloadHandlerTexture::.ctor(System.Boolean)
extern void DownloadHandlerTexture__ctor_m29775B9DEA2C54AE7EFED3550C31AB4D5CF58AD8 (void);
// 0x00000006 Unity.Collections.NativeArray`1<System.Byte> UnityEngine.Networking.DownloadHandlerTexture::GetNativeData()
extern void DownloadHandlerTexture_GetNativeData_m241724A953EC64E660235D0A0374EFACE1B0779A (void);
// 0x00000007 System.Void UnityEngine.Networking.DownloadHandlerTexture::Dispose()
extern void DownloadHandlerTexture_Dispose_m8B9EB903164BC27743144F900697F7B98A4725EB (void);
// 0x00000008 UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::get_texture()
extern void DownloadHandlerTexture_get_texture_m170B5539995EB5984E98E426F52983589F7BEB7C (void);
// 0x00000009 UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::InternalGetTexture()
extern void DownloadHandlerTexture_InternalGetTexture_mD1882B183F71176C89B42DB2C8A185BC9A647167 (void);
// 0x0000000A UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::InternalGetTextureNative()
extern void DownloadHandlerTexture_InternalGetTextureNative_m9244CDF2A5DE7B9971121CAC5642DD5FEA877688 (void);
// 0x0000000B System.Void UnityEngine.Networking.DownloadHandlerTexture::ClearNativeTexture()
extern void DownloadHandlerTexture_ClearNativeTexture_mB4763AF5AA60EF02798465D2AF89D31BFF545C10 (void);
static Il2CppMethodPointer s_methodPointers[11] = 
{
	UnityWebRequestTexture_GetTexture_mEEC74BBC5190D4012A0B87C5D439AF59E22EF2EF,
	UnityWebRequestTexture_GetTexture_m45F855106C834021AC0DFA25FE31BA14C42693CA,
	DownloadHandlerTexture_Create_m6DBB03DC64DB11F35862D7A4F6B4A814B8531A21,
	DownloadHandlerTexture_InternalCreateTexture_m2BC0B138561A55B9CCAB694E4447888B21D1B47A,
	DownloadHandlerTexture__ctor_m29775B9DEA2C54AE7EFED3550C31AB4D5CF58AD8,
	DownloadHandlerTexture_GetNativeData_m241724A953EC64E660235D0A0374EFACE1B0779A,
	DownloadHandlerTexture_Dispose_m8B9EB903164BC27743144F900697F7B98A4725EB,
	DownloadHandlerTexture_get_texture_m170B5539995EB5984E98E426F52983589F7BEB7C,
	DownloadHandlerTexture_InternalGetTexture_mD1882B183F71176C89B42DB2C8A185BC9A647167,
	DownloadHandlerTexture_InternalGetTextureNative_m9244CDF2A5DE7B9971121CAC5642DD5FEA877688,
	DownloadHandlerTexture_ClearNativeTexture_mB4763AF5AA60EF02798465D2AF89D31BFF545C10,
};
static const int32_t s_InvokerIndices[11] = 
{
	9840,
	8551,
	8504,
	4808,
	4808,
	5897,
	6247,
	6120,
	6120,
	6120,
	6247,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestTextureModule.dll",
	11,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
