%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Gaze Controller
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UpdateTrackingType
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputTracking
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputActions
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelPrefab
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelParent
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Model
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AnimateModel
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelDeSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Name
    value: Position
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_ExpectedControlType
    value: Vector3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Id
    value: c5c11aa1-bde1-4a3a-a80e-886509abbc16
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Reference
    value: 
    objectReference: {fileID: 3220680263695665919, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Name
    value: Rotation
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_ExpectedControlType
    value: Quaternion
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Id
    value: 49d34eac-625a-45e4-99f1-520d9a91587d
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Reference
    value: 
    objectReference: {fileID: -5930349909990434036, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Name
    value: Is Tracked
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Id
    value: 5c0d8bf3-8c9a-43da-89cf-8d09230caf0e
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Flags
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Reference
    value: 
    objectReference: {fileID: -831106814531524825, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Name
    value: Tracking State
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_ExpectedControlType
    value: Integer
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Id
    value: 776eea05-891d-47cc-ba9d-8353541bbc61
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Reference
    value: 
    objectReference: {fileID: 2069149553511882089, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Name
    value: Select
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Id
    value: d90b6a89-0f2d-4711-b5e3-6f709d46a3e9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Name
    value: Select Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Id
    value: 982fe616-81e7-4cea-8b1c-0fc18677699d
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Name
    value: Activate
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Id
    value: 19b691c8-8354-453a-818d-9ab6fc719aa7
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Name
    value: Activate Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Id
    value: 8f68251c-035b-44ae-9665-8d736bde8dbe
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Name
    value: UI Press
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Id
    value: cdf9e438-d5dd-488a-b24c-92607b3cf39e
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Name
    value: UI Press Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Id
    value: d4ffb96f-9b0b-4d82-93a2-b7bd82d288c9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Name
    value: UI Scroll
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Name
    value: Haptic Device
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Type
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Id
    value: 9b83662a-0fd2-4aee-aae5-c6634d0fbafc
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Name
    value: Rotate Anchor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Id
    value: 4cae4fad-cc16-4684-8fec-1ba47667d275
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Name
    value: Directional Anchor Rotation
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Id
    value: f58ccfc7-633b-46cf-9164-6dfafd85bf99
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Name
    value: Translate Anchor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Id
    value: 83996765-f9ca-47e0-969e-b8fa763170db
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Name
    value: Scale Toggle
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Id
    value: c491f7d4-d5f5-485e-8ebe-d323ea5e8eee
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_UseReference
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Name
    value: Scale Delta
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Id
    value: 702606e7-26dd-4ff2-8e26-59078e8cafee
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Reference
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ButtonPressPoint
    value: 0.5
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
