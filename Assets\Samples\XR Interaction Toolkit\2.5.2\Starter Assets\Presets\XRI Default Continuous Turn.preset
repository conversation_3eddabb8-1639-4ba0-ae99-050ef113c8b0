%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Continuous Turn
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: 919e39492806b334982b6b84c90dd927, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_System
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TurnSpeed
    value: 60
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_Name
    value: Left Hand Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_Id
    value: b9ac2485-f305-451c-9106-d1d25cc235dc
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LeftHandTurnAction.m_Reference
    value: 
    objectReference: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_Name
    value: Right Hand Turn
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_Id
    value: 93119ec4-ae3b-41c0-8fb4-7ff4c5e6f732
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RightHandTurnAction.m_Reference
    value: 
    objectReference: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_ExcludedProperties: []
