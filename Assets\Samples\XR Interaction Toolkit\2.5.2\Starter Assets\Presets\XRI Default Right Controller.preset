%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Right Controller
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UpdateTrackingType
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputTracking
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputActions
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelPrefab
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelParent
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Model
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AnimateModel
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelDeSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_ExpectedControlType
    value: Vector3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Reference
    value: 
    objectReference: {fileID: -3326005586356538449, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_ExpectedControlType
    value: Quaternion
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Reference
    value: 
    objectReference: {fileID: 5101698808175986029, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Flags
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Reference
    value: 
    objectReference: {fileID: -7044516463258014562, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Name
    value: Tracking State
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_ExpectedControlType
    value: Integer
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Id
    value: 008dba4e-870a-43fb-9a1f-1a7bc3ecec0c
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Reference
    value: 
    objectReference: {fileID: -1277054153949319361, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Reference
    value: 
    objectReference: {fileID: 187161793506945269, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Name
    value: Select Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Id
    value: 6b1e5826-d74e-452e-ab31-5d6eae6f407e
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Reference
    value: 
    objectReference: {fileID: -1758520528963094988, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Reference
    value: 
    objectReference: {fileID: 83097790271614945, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Name
    value: Activate Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Id
    value: 98d3d870-d1c9-4fbe-9790-8d0c2cb9ffc0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Reference
    value: 
    objectReference: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Reference
    value: 
    objectReference: {fileID: 3279264004350380116, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Name
    value: UI Press Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Id
    value: bf4ab5bd-3648-4de6-a1f6-8e879b2612c2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Reference
    value: 
    objectReference: {fileID: -5908353012961274365, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Name
    value: UI Scroll
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Id
    value: a6c0ac1e-4065-4abc-ac84-e81172fbfdd4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Reference
    value: 
    objectReference: {fileID: -6756787485274679044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Name
    value: Haptic Device
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Type
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Id
    value: 59ea1b94-e9f8-4049-ab97-5920b11143a5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Reference
    value: 
    objectReference: {fileID: -8222252007134549311, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Reference
    value: 
    objectReference: {fileID: -5913262927076077117, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Name
    value: Directional Anchor Rotation
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Id
    value: 72b93609-c58e-411b-a958-c221860f8269
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Reference
    value: 
    objectReference: {fileID: -440298646266941818, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Name
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Id
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Reference
    value: 
    objectReference: {fileID: 875253871413052681, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Name
    value: Scale Toggle
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Id
    value: 0ec63ab1-52db-4370-be3a-274ee310dae9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Reference
    value: 
    objectReference: {fileID: -2524354804938687746, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Name
    value: Scale Delta
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Id
    value: 693cabdd-8776-492d-8641-2f6adc511d4c
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Reference
    value: 
    objectReference: {fileID: -6447266317303757838, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ButtonPressPoint
    value: 0.5
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
