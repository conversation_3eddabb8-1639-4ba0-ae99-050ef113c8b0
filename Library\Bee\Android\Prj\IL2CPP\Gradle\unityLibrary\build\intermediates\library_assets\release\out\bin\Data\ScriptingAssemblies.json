{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.AutoStreamingModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UIElementsNativeModule.dll", "UnityEngine.UIWidgetsModule.dll", "UnityEngine.UNETModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp.dll", "PICO.TobSupport.dll", "Unity.Timeline.dll", "Unity.TextMeshPro.dll", "Unity.XR.Interaction.Toolkit.dll", "Pico.Spatializer.dll", "Unity.VisualScripting.Flow.dll", "Unity.InputSystem.dll", "UnityEngine.SpatialTracking.dll", "UnityEngine.UI.dll", "Unity.VisualScripting.Core.dll", "Unity.Mathematics.dll", "Cinemachine.dll", "Unity.XR.PICO.dll", "Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator.dll", "UnityEngine.XR.LegacyInputHelpers.dll", "Unity.InputSystem.ForUI.dll", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll", "Unity.XR.CoreUtils.dll", "Unity.XR.Management.dll", "PICO.Platform.dll", "Pico.Spatializer.Example.dll", "Unity.VisualScripting.State.dll", "Unity.VisualScripting.Antlr3.Runtime.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}