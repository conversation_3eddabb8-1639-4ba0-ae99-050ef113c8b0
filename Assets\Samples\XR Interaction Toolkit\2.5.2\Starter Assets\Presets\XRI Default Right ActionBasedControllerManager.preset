%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Right ActionBasedControllerManager
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: f9ac216f0eb04754b1d938aac6380b31, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ManipulationInteractionGroup
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectInteractor
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RayInteractor
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TeleportInteractor
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TeleportModeActivate
    value: 
    objectReference: {fileID: -8061240218431744966, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TeleportModeCancel
    value: 
    objectReference: {fileID: 2307464322626738743, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_Turn
    value: 
    objectReference: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SnapTurn
    value: 
    objectReference: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_Move
    value: 
    objectReference: {fileID: -8198699208435500284, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIScroll
    value: 
    objectReference: {fileID: -6756787485274679044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SmoothMotionEnabled
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SmoothTurnEnabled
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollingEnabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RayInteractorChanged.m_PersistentCalls.m_Calls.Array.size
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
