%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default Left Controller
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: caff514de9b15ad48ab85dcff5508221, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UpdateTrackingType
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputTracking
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputActions
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelPrefab
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelParent
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Model
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AnimateModel
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelDeSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Name
    value: Position
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_ExpectedControlType
    value: Vector3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Id
    value: 8b170a9b-132e-486d-947e-6a244d4362ea
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PositionAction.m_Reference
    value: 
    objectReference: {fileID: -2024308242397127297, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Name
    value: Rotation
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_ExpectedControlType
    value: Quaternion
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Id
    value: 080819c2-8547-4beb-8522-e6356be16fb1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotationAction.m_Reference
    value: 
    objectReference: {fileID: 8248158260566104461, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Name
    value: Is Tracked
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Id
    value: 22c1da5c-d38f-4253-a25c-fe94205f2ec5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Action.m_Flags
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_IsTrackedAction.m_Reference
    value: 
    objectReference: {fileID: 840156964685210860, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Name
    value: Tracking State
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_ExpectedControlType
    value: Integer
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Id
    value: f3874727-df53-4207-8cd4-6248164663d7
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TrackingStateAction.m_Reference
    value: 
    objectReference: {fileID: 684395432459739428, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Name
    value: Select
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Id
    value: 8e000d1c-13a4-4cc0-ad37-f2e125874399
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectAction.m_Reference
    value: 
    objectReference: {fileID: -6131295136447488360, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Name
    value: Select Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Id
    value: e015d020-ed5c-40b6-b968-fa9881521f0e
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SelectActionValue.m_Reference
    value: 
    objectReference: {fileID: 6558622148059887818, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Name
    value: Activate
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Id
    value: 3995f9f4-6aa7-409a-80d2-5f7ea1464fde
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateAction.m_Reference
    value: 
    objectReference: {fileID: -5982496924579745919, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Name
    value: Activate Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Id
    value: 492aea1c-7d58-4cb0-8e3c-257d2f651c04
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ActivateActionValue.m_Reference
    value: 
    objectReference: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Name
    value: UI Press
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_ExpectedControlType
    value: Button
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Id
    value: db89d01c-df6f-4954-b868-103dd5bdb514
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressAction.m_Reference
    value: 
    objectReference: {fileID: -6395602842196007441, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Name
    value: UI Press Action Value
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Id
    value: 6258f0cd-e000-49ea-b3b6-7c930f12c390
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIPressActionValue.m_Reference
    value: 
    objectReference: {fileID: 71106601250685021, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Name
    value: UI Scroll
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Id
    value: b74fcfe3-d94d-4bf1-960a-364568ffe66b
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UIScrollAction.m_Reference
    value: 
    objectReference: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Name
    value: Haptic Device
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Type
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Id
    value: 3e09b626-c80d-40ec-9592-eb3fe89c2038
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_HapticDeviceAction.m_Reference
    value: 
    objectReference: {fileID: -8785819595477538065, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Name
    value: Rotate Anchor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Id
    value: 3dca8766-e652-4e78-8406-420aa73ba338
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_RotateAnchorAction.m_Reference
    value: 
    objectReference: {fileID: -7363382999065477798, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Name
    value: Directional Anchor Rotation
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Id
    value: 7d323aae-15a7-4c32-a2b9-0653cb108725
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DirectionalAnchorRotationAction.m_Reference
    value: 
    objectReference: {fileID: -8811388872089202044, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Name
    value: Translate Anchor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Id
    value: e873605e-6a95-4389-8fbe-39069340ba92
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TranslateAnchorAction.m_Reference
    value: 
    objectReference: {fileID: 7779212132400271959, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Name
    value: Scale Toggle
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Type
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_ExpectedControlType
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Id
    value: f154653e-fb1f-4aa0-b5a4-b7541ef2cad9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleToggleAction.m_Reference
    value: 
    objectReference: {fileID: -335775248641796371, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Name
    value: Scale Delta
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Id
    value: a45a321f-4e2e-479e-a3ab-da25a505e44e
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScaleDeltaAction.m_Reference
    value: 
    objectReference: {fileID: -1636515391019944688, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ButtonPressPoint
    value: 0.5
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
