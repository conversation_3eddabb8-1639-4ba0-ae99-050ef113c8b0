%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRI Default XR Screen Space Controller
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: b7e4c509417774e7e8a8784a2739de68, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_UpdateTrackingType
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputTracking
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInputActions
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelPrefab
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelParent
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Model
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_AnimateModel
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ModelDeSelectTransition
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableTouchscreenGestureInputController
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_Name
    value: Tap Start Position
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_Id
    value: 92224516-b7ff-4476-bcee-ac90d78580f7
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TapStartPositionAction.m_Reference
    value: 
    objectReference: {fileID: 2494954584338170553, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_Name
    value: Drag Current Position
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_Id
    value: f1e3548b-3ed6-4e99-a0a4-be61acca187a
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragCurrentPositionAction.m_Reference
    value: 
    objectReference: {fileID: -7530398834462728267, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_Name
    value: Drag Delta
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_Id
    value: 225cbc26-23a5-4084-9eca-ef3b0aedf80f
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DragDeltaAction.m_Reference
    value: 
    objectReference: {fileID: -3603844561257126198, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_Name
    value: Pinch Start Position
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_Id
    value: 23bda604-799e-4d5c-a28c-a7aa89ae7d7e
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchStartPositionAction.m_Reference
    value: 
    objectReference: {fileID: 9070054806217310167, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_Name
    value: Pinch Gap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_Id
    value: ef16b2f3-a8bb-40e9-8e11-29c3c7fffcde
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapAction.m_Reference
    value: 
    objectReference: {fileID: 3166769696172522120, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_Name
    value: Pinch Gap Delta
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_Id
    value: 818c3483-a72e-402c-b79d-eed7878ec7fd
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_PinchGapDeltaAction.m_Reference
    value: 
    objectReference: {fileID: -5112888916153672211, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_Name
    value: Twist Start Position
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_ExpectedControlType
    value: Vector2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_Id
    value: abd8efdb-6861-4448-8ad7-061bf538d6ec
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistStartPositionAction.m_Reference
    value: 
    objectReference: {fileID: -1941033212670429761, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_Name
    value: Twist Delta Rotation
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_ExpectedControlType
    value: Axis
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_Id
    value: 8c4e1c5a-020b-4b79-b1a9-11c167503b70
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_TwistDeltaRotationAction.m_Reference
    value: 
    objectReference: {fileID: -8156239294363760665, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_UseReference
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_Name
    value: Screen Touch Count
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_Type
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_ExpectedControlType
    value: Integer
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_Id
    value: 909c4e64-82b6-4d74-9913-2d58aff78cad
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_Processors
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_Interactions
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_SingletonActionBindings.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Action.m_Flags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_ScreenTouchCountAction.m_Reference
    value: 
    objectReference: {fileID: 4162966010302970412, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ControllerCamera
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_BlockInteractionsWithScreenSpaceUI
    value: 1
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
